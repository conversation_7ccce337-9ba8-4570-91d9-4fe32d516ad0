'use client'

import React from 'react'
import { motion } from 'framer-motion'
import {
  CreditCardIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ArrowPathIcon,
  DocumentTextIcon,
  EyeIcon,
  PencilIcon,
  ArrowDownTrayIcon,
  TrashIcon
} from '@heroicons/react/24/outline'

interface Payment {
  id: string | number
  amount: number
  paymentDate: string
  paymentMethod: string
  status: string
  notes?: string
  invoiceId: string | number
  createdAt: string
  updatedAt?: string
  currency?: string
  transactionId?: string
  processingFee?: number
  netAmount?: number
  refundedAmount?: number
  stripePaymentIntentId?: string
  paypalOrderId?: string
  promoCode?: string
  discountAmount?: number
  billingAddress?: any
  receiptEmail?: string
  metadata?: any
}

interface PaymentRowProps {
  payment: Payment
  isSelected: boolean
  onSelect: () => void
  onAction: (action: string) => void
  onPaymentSelect: (payment: Payment | null) => void
  selectedPayment: Payment | null
  visibleColumns: string[]
  displayDensity: string
  viewMode: string
  actionLoading: string | null
  formatDate: (date: string) => string
  formatCurrency: (amount: number, currency?: string) => string
  getStatusColor: (status: string) => string
  getStatusIcon: (status: string) => React.ReactNode
  getPaymentMethodIcon: (method: string) => React.ReactNode
}

export function PaymentRow({
  payment,
  isSelected,
  onSelect,
  onAction,
  onPaymentSelect,
  selectedPayment,
  visibleColumns,
  displayDensity,
  viewMode,
  actionLoading,
  formatDate,
  formatCurrency,
  getStatusColor,
  getStatusIcon,
  getPaymentMethodIcon
}: PaymentRowProps) {
  const isCompact = displayDensity === 'compact'
  const isCurrentlySelected = selectedPayment?.id === payment.id

  // List View (Table Row)
  if (viewMode === 'list') {
    return (
      <tr
        className={`cursor-pointer transition-colors ${
          isCurrentlySelected
            ? 'bg-yellow-50 border-yellow-200'
            : 'hover:bg-gray-50'
        }`}
        onClick={() => onPaymentSelect(payment)}
      >
        <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
          <input
            type="checkbox"
            checked={isSelected}
            onChange={onSelect}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
        </td>
        
        {visibleColumns.includes('amount') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm font-medium text-gray-900">
              {formatCurrency(payment.amount, payment.currency)}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('status') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
              {getStatusIcon(payment.status)}
              <span className="ml-1">{payment.status}</span>
            </span>
          </td>
        )}
        
        {visibleColumns.includes('paymentDate') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm text-gray-900">{formatDate(payment.paymentDate)}</div>
          </td>
        )}
        
        {visibleColumns.includes('paymentMethod') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="flex items-center">
              {getPaymentMethodIcon(payment.paymentMethod)}
              <span className="ml-2 text-sm text-gray-900">{payment.paymentMethod}</span>
            </div>
          </td>
        )}
        
        {visibleColumns.includes('notes') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'}`}>
            <div className="text-sm text-gray-900 max-w-xs truncate">
              {payment.notes || '-'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('currency') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm text-gray-900">
              {payment.currency || 'USD'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('transactionId') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm text-gray-900 font-mono">
              {payment.transactionId ? payment.transactionId.substring(0, 12) + '...' : '-'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('processingFee') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm text-gray-900">
              {payment.processingFee ? formatCurrency(payment.processingFee, payment.currency) : '-'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('netAmount') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm text-gray-900">
              {payment.netAmount ? formatCurrency(payment.netAmount, payment.currency) : '-'}
            </div>
          </td>
        )}
        
        {visibleColumns.includes('createdAt') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm text-gray-500">{formatDate(payment.createdAt)}</div>
          </td>
        )}
        
        {visibleColumns.includes('updatedAt') && (
          <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap`}>
            <div className="text-sm text-gray-500">
              {payment.updatedAt ? formatDate(payment.updatedAt) : '-'}
            </div>
          </td>
        )}
        
        <td className={`px-6 ${isCompact ? 'py-2' : 'py-4'} whitespace-nowrap text-right text-sm font-medium`}>
          <div className="flex items-center justify-end space-x-2">
            <button
              onClick={(e) => {
                e.stopPropagation()
                onPaymentSelect(payment)
              }}
              className="p-1.5 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-md transition-colors"
              title="View Payment"
            >
              <EyeIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => onAction('edit')}
              disabled={actionLoading === `edit-${payment.id}`}
              className="p-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
              title="Edit Payment"
            >
              <PencilIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => onAction('download')}
              className="p-1.5 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-md transition-colors"
              title="Download Receipt"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
            </button>
            {payment.status === 'completed' && (
              <button
                onClick={() => onAction('refund')}
                className="p-1.5 text-orange-600 hover:text-orange-800 hover:bg-orange-50 rounded-md transition-colors"
                title="Refund Payment"
              >
                <ArrowPathIcon className="h-4 w-4" />
              </button>
            )}
            <button
              onClick={() => onAction('delete')}
              disabled={actionLoading === `delete-${payment.id}`}
              className="p-1.5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50"
              title="Delete Payment"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </td>
      </tr>
    )
  }

  // Grid View (Compact Card)
  if (viewMode === 'grid') {
    return (
      <motion.div
        onClick={() => onPaymentSelect(payment)}
        className={`border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-md ${
          isCurrentlySelected
            ? 'border-yellow-500 bg-yellow-50 shadow-md'
            : 'border-gray-200 hover:border-gray-300 bg-white'
        }`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={onSelect}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <div className="text-lg font-semibold text-gray-900">
              {formatCurrency(payment.amount, payment.currency)}
            </div>
          </div>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
            {getStatusIcon(payment.status)}
            <span className="ml-1">{payment.status}</span>
          </span>
        </div>
        
        <div className="space-y-2 text-sm">
          <div className="flex items-center text-gray-600">
            <CalendarIcon className="h-4 w-4 mr-2" />
            <span>{formatDate(payment.paymentDate)}</span>
          </div>
          
          <div className="flex items-center text-gray-600">
            {getPaymentMethodIcon(payment.paymentMethod)}
            <span className="ml-2">{payment.paymentMethod}</span>
          </div>
          
          {payment.notes && (
            <div className="text-gray-600 line-clamp-2">
              {payment.notes}
            </div>
          )}
        </div>
        
        <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-200">
          <div className="text-xs text-gray-500">
            {formatDate(payment.createdAt)}
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={(e) => {
                e.stopPropagation()
                onPaymentSelect(payment)
              }}
              className="p-1.5 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-md transition-colors"
              title="View"
            >
              <EyeIcon className="h-3 w-3" />
            </button>
            <button
              onClick={() => onAction('edit')}
              disabled={actionLoading === `edit-${payment.id}`}
              className="p-1.5 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
              title="Edit"
            >
              <PencilIcon className="h-3 w-3" />
            </button>
            <button
              onClick={() => onAction('download')}
              className="p-1.5 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-md transition-colors"
              title="Download"
            >
              <ArrowDownTrayIcon className="h-3 w-3" />
            </button>
            {payment.status === 'completed' && (
              <button
                onClick={() => onAction('refund')}
                className="p-1.5 text-orange-600 hover:text-orange-800 hover:bg-orange-50 rounded-md transition-colors"
                title="Refund"
              >
                <ArrowPathIcon className="h-3 w-3" />
              </button>
            )}
            <button
              onClick={() => onAction('delete')}
              disabled={actionLoading === `delete-${payment.id}`}
              className="p-1.5 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50"
              title="Delete"
            >
              <TrashIcon className="h-3 w-3" />
            </button>
          </div>
        </div>
      </motion.div>
    )
  }

  // Card View (Detailed Card)
  return (
    <motion.div
      onClick={() => onPaymentSelect(payment)}
      className={`border-2 rounded-lg p-6 cursor-pointer transition-all duration-200 hover:shadow-lg ${
        isCurrentlySelected
          ? 'border-yellow-500 bg-yellow-50 shadow-md'
          : 'border-gray-200 hover:border-gray-300 bg-white'
      }`}
      whileHover={{ scale: 1.01 }}
      whileTap={{ scale: 0.99 }}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-3 mb-4">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={onSelect}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <h3 className="text-lg font-medium text-gray-900">
              {formatCurrency(payment.amount, payment.currency)}
            </h3>

            {/* Status Badge */}
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
              {getStatusIcon(payment.status)}
              <span className="ml-1">{payment.status}</span>
            </span>
          </div>

          {/* Payment Notes */}
          {payment.notes && (
            <p className="mt-2 text-sm text-gray-600 line-clamp-2">
              {payment.notes}
            </p>
          )}

          {/* Payment Details Grid */}
          <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center text-gray-600">
              <CalendarIcon className="h-4 w-4 mr-2 text-blue-600" />
              <span>{formatDate(payment.paymentDate)}</span>
            </div>

            <div className="flex items-center text-gray-600">
              {getPaymentMethodIcon(payment.paymentMethod)}
              <span className="ml-2">{payment.paymentMethod}</span>
            </div>

            {payment.currency && payment.currency !== 'USD' && (
              <div className="flex items-center text-gray-600">
                <CurrencyDollarIcon className="h-4 w-4 mr-2 text-green-600" />
                <span>{payment.currency}</span>
              </div>
            )}

            {payment.transactionId && (
              <div className="flex items-center text-gray-600">
                <DocumentTextIcon className="h-4 w-4 mr-2 text-purple-600" />
                <span className="font-mono text-xs">{payment.transactionId.substring(0, 16)}...</span>
              </div>
            )}

            {payment.processingFee && payment.processingFee > 0 && (
              <div className="flex items-center text-gray-600">
                <span className="text-xs">Fee: {formatCurrency(payment.processingFee, payment.currency)}</span>
              </div>
            )}

            {payment.netAmount && (
              <div className="flex items-center text-gray-600">
                <span className="text-xs">Net: {formatCurrency(payment.netAmount, payment.currency)}</span>
              </div>
            )}
          </div>

          {/* Additional Information */}
          {(payment.promoCode || payment.discountAmount || payment.refundedAmount) && (
            <div className="mt-4 flex flex-wrap items-center gap-4 text-sm text-gray-500">
              {payment.promoCode && (
                <div>
                  <span className="font-medium">Promo:</span> {payment.promoCode}
                </div>
              )}
              {payment.discountAmount && payment.discountAmount > 0 && (
                <div>
                  <span className="font-medium">Discount:</span> {formatCurrency(payment.discountAmount, payment.currency)}
                </div>
              )}
              {payment.refundedAmount && payment.refundedAmount > 0 && (
                <div>
                  <span className="font-medium">Refunded:</span> {formatCurrency(payment.refundedAmount, payment.currency)}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Amount Breakdown */}
        <div className="ml-6 text-right">
          <div className="text-2xl font-bold text-gray-900">
            {formatCurrency(payment.amount, payment.currency)}
          </div>
          {payment.netAmount && payment.netAmount !== payment.amount && (
            <div className="text-sm text-gray-500">
              Net: {formatCurrency(payment.netAmount, payment.currency)}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-end space-x-2 mt-4">
            <button
              onClick={(e) => {
                e.stopPropagation()
                onPaymentSelect(payment)
              }}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-md transition-colors"
              title="View Payment"
            >
              <EyeIcon className="h-4 w-4" />
            </button>

            <button
              onClick={() => onAction('edit')}
              disabled={actionLoading === `edit-${payment.id}`}
              className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
              title="Edit Payment"
            >
              <PencilIcon className="h-4 w-4" />
            </button>

            <button
              onClick={() => onAction('download')}
              className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-md transition-colors"
              title="Download Receipt"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
            </button>

            {payment.status === 'completed' && (
              <button
                onClick={() => onAction('refund')}
                className="p-2 text-orange-600 hover:text-orange-800 hover:bg-orange-50 rounded-md transition-colors"
                title="Refund Payment"
              >
                <ArrowPathIcon className="h-4 w-4" />
              </button>
            )}

            <button
              onClick={() => onAction('delete')}
              disabled={actionLoading === `delete-${payment.id}`}
              className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors disabled:opacity-50"
              title="Delete Payment"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Footer with Dates */}
      <div className="mt-6 border-t border-gray-200 pt-4">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div>
            Created {formatDate(payment.createdAt)}
          </div>
          {payment.updatedAt && (
            <div>
              Updated {formatDate(payment.updatedAt)}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}
