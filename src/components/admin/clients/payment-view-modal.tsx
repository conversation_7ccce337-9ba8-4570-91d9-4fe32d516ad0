'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  XMarkIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  CreditCardIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  UserIcon,
  BuildingOfficeIcon,
  BanknotesIcon,
  ReceiptPercentIcon,
  DocumentTextIcon,
  GlobeAltIcon,
  BuildingLibraryIcon,
  DevicePhoneMobileIcon,
  ShieldCheckIcon,
  ArrowPathIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline'

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Project {
  id: string | number
  name: string
  description: string
  status?: string
}

interface Invoice {
  id: string | number
  totalAmount: number
  status: string
  dueDate: string
  description?: string
}

interface Payment {
  id: string | number
  amount: number
  paymentDate: string
  paymentMethod: string
  status: string
  notes?: string
  invoiceId: string | number
  createdAt: string
  updatedAt?: string
  currency?: string
  transactionId?: string
  processingFee?: number
  netAmount?: number
  refundedAmount?: number
  stripePaymentIntentId?: string
  paypalOrderId?: string
  promoCode?: string
  discountAmount?: number
  billingAddress?: any
  receiptEmail?: string
  metadata?: any
}

interface PaymentViewModalProps {
  isOpen: boolean
  onClose: () => void
  payment: Payment
  client: Client
  project?: Project
  invoice: Invoice
}

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'succeeded':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'failed':
    case 'declined':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'refunded':
      return 'bg-purple-100 text-purple-800 border-purple-200'
    case 'processing':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getStatusIcon = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'succeeded':
      return <CheckCircleIcon className="h-4 w-4" />
    case 'pending':
      return <ClockIcon className="h-4 w-4" />
    case 'failed':
    case 'declined':
      return <ExclamationTriangleIcon className="h-4 w-4" />
    case 'refunded':
      return <ArrowPathIcon className="h-4 w-4" />
    case 'processing':
      return <ExclamationCircleIcon className="h-4 w-4" />
    default:
      return <DocumentTextIcon className="h-4 w-4" />
  }
}

const getPaymentMethodIcon = (method: string) => {
  const methodLower = method?.toLowerCase()
  if (methodLower?.includes('card') || methodLower?.includes('stripe')) {
    return <CreditCardIcon className="h-5 w-5" />
  } else if (methodLower?.includes('paypal')) {
    return <GlobeAltIcon className="h-5 w-5" />
  } else if (methodLower?.includes('bank') || methodLower?.includes('transfer') || methodLower?.includes('ach')) {
    return <BuildingLibraryIcon className="h-5 w-5" />
  } else if (methodLower?.includes('apple') || methodLower?.includes('google')) {
    return <DevicePhoneMobileIcon className="h-5 w-5" />
  } else {
    return <BanknotesIcon className="h-5 w-5" />
  }
}

const formatCurrency = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export default function PaymentViewModal({
  isOpen,
  onClose,
  payment,
  client,
  project,
  invoice
}: PaymentViewModalProps) {
  if (!isOpen) return null

  const netAmount = payment.netAmount || (payment.amount - (payment.processingFee || 0))
  const discountAmount = payment.discountAmount || 0

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          {/* Background overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
            onClick={onClose}
          />

          {/* Modal panel */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative inline-block align-bottom bg-white rounded-lg shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-purple-600 to-purple-700 px-6 py-4 rounded-t-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <CreditCardIcon className="h-6 w-6 text-white" />
                  <h2 className="text-xl font-bold text-white">Payment Details</h2>
                </div>
                <button
                  onClick={onClose}
                  className="text-white hover:text-gray-200 transition-colors"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
              {/* Payment Header */}
              <div className="mb-8">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      Payment #{payment.id}
                    </h3>
                    {payment.notes && (
                      <p className="text-gray-600 leading-relaxed">{payment.notes}</p>
                    )}
                  </div>
                  <div className="ml-6 flex-shrink-0">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(payment.status)}`}>
                      {getStatusIcon(payment.status)}
                      <span className="ml-1 capitalize">{payment.status}</span>
                    </span>
                  </div>
                </div>

                {/* Amount Summary */}
                <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6 border border-purple-200">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600 mb-2">
                      {formatCurrency(payment.amount, payment.currency)}
                    </div>
                    <div className="text-sm text-gray-600">Payment Amount</div>
                  </div>
                </div>
              </div>

              {/* Main Content Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Left Column - Payment Details */}
                <div className="lg:col-span-2 space-y-6">
                  {/* Payment Method & Transaction */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      {getPaymentMethodIcon(payment.paymentMethod)}
                      <span className="ml-2">Payment Method & Transaction</span>
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                        <div className="text-gray-900 font-medium capitalize">{payment.paymentMethod.replace('_', ' ')}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Currency</label>
                        <div className="text-gray-900">{payment.currency || 'USD'}</div>
                      </div>
                      {payment.transactionId && (
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Transaction ID</label>
                          <div className="text-gray-900 font-mono text-sm">{payment.transactionId}</div>
                        </div>
                      )}
                      {payment.stripePaymentIntentId && (
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Stripe Payment Intent</label>
                          <div className="text-gray-900 font-mono text-sm">{payment.stripePaymentIntentId}</div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Financial Breakdown */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <ReceiptPercentIcon className="h-5 w-5 mr-2 text-green-600" />
                      Financial Breakdown
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Payment Amount</span>
                        <span className="font-semibold text-gray-900">{formatCurrency(payment.amount, payment.currency)}</span>
                      </div>
                      {discountAmount > 0 && (
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Discount Applied</span>
                          <span className="font-semibold text-green-600">-{formatCurrency(discountAmount, payment.currency)}</span>
                        </div>
                      )}
                      {payment.processingFee && payment.processingFee > 0 && (
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Processing Fee</span>
                          <span className="font-semibold text-red-600">{formatCurrency(payment.processingFee, payment.currency)}</span>
                        </div>
                      )}
                      <div className="border-t border-gray-200 pt-3">
                        <div className="flex justify-between items-center">
                          <span className="text-lg font-semibold text-gray-900">Net Amount</span>
                          <span className="text-lg font-bold text-purple-600">{formatCurrency(netAmount, payment.currency)}</span>
                        </div>
                      </div>
                      {payment.refundedAmount && payment.refundedAmount > 0 && (
                        <div className="flex justify-between items-center border-t border-gray-200 pt-3">
                          <span className="text-gray-600">Refunded Amount</span>
                          <span className="font-semibold text-purple-600">{formatCurrency(payment.refundedAmount, payment.currency)}</span>
                        </div>

                {/* Right Column - Client & Meta Info */}
                <div className="space-y-6">
                  {/* Client Information */}
                  <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <BuildingOfficeIcon className="h-5 w-5 mr-2 text-blue-600" />
                      Client Information
                    </h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Company</label>
                        <div className="text-gray-900 font-medium">{client.companyName}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Contact Person</label>
                        <div className="text-gray-900 flex items-center">
                          <UserIcon className="h-4 w-4 mr-1 text-gray-500" />
                          {client.contactName}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <div className="text-gray-900">{client.contactEmail}</div>
                      </div>
                    </div>
                  </div>

                  {/* Invoice Information */}
                  <div className="bg-green-50 rounded-lg p-6 border border-green-200">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <BanknotesIcon className="h-5 w-5 mr-2 text-green-600" />
                      Related Invoice
                    </h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Invoice ID</label>
                        <div className="text-gray-900 font-mono text-sm">{invoice.id}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Invoice Total</label>
                        <div className="text-gray-900 font-semibold">{formatCurrency(invoice.totalAmount, payment.currency)}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Invoice Status</label>
                        <div className="text-gray-900 capitalize">{invoice.status}</div>
                      </div>
                      {invoice.description && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                          <div className="text-gray-900 text-sm">{invoice.description}</div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Project Information */}
                  {project && (
                    <div className="bg-purple-50 rounded-lg p-6 border border-purple-200">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <DocumentTextIcon className="h-5 w-5 mr-2 text-purple-600" />
                        Related Project
                      </h4>
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Project Name</label>
                          <div className="text-gray-900 font-medium">{project.name}</div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Project ID</label>
                          <div className="text-gray-900 font-mono text-sm">{project.id}</div>
                        </div>
                        {project.status && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <div className="text-gray-900 capitalize">{project.status.replace('_', ' ')}</div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Payment Metadata */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Payment Metadata</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Payment ID</label>
                        <div className="text-gray-900 font-mono text-sm">{payment.id}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Invoice ID</label>
                        <div className="text-gray-900 font-mono text-sm">{payment.invoiceId}</div>
                      </div>
                      {payment.receiptEmail && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Receipt Email</label>
                          <div className="text-gray-900">{payment.receiptEmail}</div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Security & Verification */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <ShieldCheckIcon className="h-5 w-5 mr-2 text-green-600" />
                      Security & Verification
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-gray-700">Payment Verified</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-gray-700">Secure Transaction</span>
                      </div>
                      {payment.stripePaymentIntentId && (
                        <div className="flex items-center space-x-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-600" />
                          <span className="text-sm text-gray-700">Stripe Verified</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-50 px-6 py-4 rounded-b-lg border-t border-gray-200">
              <div className="flex justify-end">
                <button
                  onClick={onClose}
                  className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
                      )}
                    </div>
                  </div>

                  {/* Timeline Section */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <CalendarIcon className="h-5 w-5 mr-2 text-blue-600" />
                      Payment Timeline
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Payment Date</label>
                        <div className="text-gray-900">{formatDate(payment.paymentDate)}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Created</label>
                        <div className="text-gray-900">{formatDate(payment.createdAt)}</div>
                      </div>
                      {payment.updatedAt && (
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                          <div className="text-gray-900">{formatDate(payment.updatedAt)}</div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Promotional Information */}
                  {payment.promoCode && (
                    <div className="bg-green-50 rounded-lg p-6 border border-green-200">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">Promotional Information</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Promo Code</span>
                          <span className="font-semibold text-green-600">{payment.promoCode}</span>
                        </div>
                        {discountAmount > 0 && (
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">Discount Amount</span>
                            <span className="font-semibold text-green-600">{formatCurrency(discountAmount, payment.currency)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
