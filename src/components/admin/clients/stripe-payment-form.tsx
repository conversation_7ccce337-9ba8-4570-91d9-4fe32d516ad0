'use client'

import React, { useState } from 'react'
import {
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js'
import { motion } from 'framer-motion'
import {
  CreditCardIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

interface StripePaymentFormProps {
  clientSecret: string
  onSuccess: (paymentIntent: any) => void
  onError: (error: string) => void
  amount: number
  currency: string
}

export function StripePaymentForm({
  clientSecret,
  onSuccess,
  onError,
  amount,
  currency
}: StripePaymentFormProps) {
  const stripe = useStripe()
  const elements = useElements()
  const [isLoading, setIsLoading] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  const handleSubmit = async () => {

    if (!stripe || !elements) {
      setErrorMessage('Stripe is not properly loaded. Please refresh the page and try again.')
      return
    }

    setIsLoading(true)
    setErrorMessage(null)
    setSuccessMessage(null)

    try {
      // Get the CardElement
      const cardElement = elements.getElement('card')

      if (!cardElement) {
        setErrorMessage('Card element not found. Please refresh the page and try again.')
        return
      }

      // Confirm the payment with Stripe
      const { error: stripeError, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
        }
      })

      if (stripeError) {
        // Log for debugging (only in console, not as error)
        console.log('Stripe payment declined - Type:', stripeError.type)
        console.log('Stripe payment declined - Code:', stripeError.code)
        console.log('Stripe payment declined - Message:', stripeError.message)

        // Create user-friendly error message
        let userMessage = 'Payment was declined. Please check your card details and try again.'

        if (stripeError.message) {
          userMessage = stripeError.message
        } else if (stripeError.code === 'card_declined') {
          userMessage = 'Your card was declined. Please try a different payment method.'
        } else if (stripeError.code === 'insufficient_funds') {
          userMessage = 'Your card has insufficient funds. Please try a different card.'
        } else if (stripeError.code === 'expired_card') {
          userMessage = 'Your card has expired. Please use a different card.'
        } else if (stripeError.code === 'incorrect_cvc') {
          userMessage = 'Your card\'s security code is incorrect. Please check and try again.'
        }

        setErrorMessage(userMessage)
        onError(userMessage)
        return
      }

      // Validate payment status
      if (paymentIntent.status !== 'succeeded') {
        console.log('Payment intent status:', paymentIntent.status)
        setErrorMessage('Payment was not completed successfully. Please try again.')
        onError('Payment was not completed successfully. Please try again.')
        return
      }

      // Payment succeeded
      setSuccessMessage('Payment processed successfully!')
      onSuccess(paymentIntent)

    } catch (error) {
      console.log('Payment submission error:', error)
      const errorMsg = error instanceof Error ? error.message : 'Failed to process payment. Please try again.'
      setErrorMessage(errorMsg)
      onError(errorMsg)
    } finally {
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      {/* Payment Amount Display */}
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 border border-purple-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CreditCardIcon className="h-5 w-5 text-purple-600" />
            <span className="text-sm font-medium text-gray-700">Payment Amount</span>
          </div>
          <span className="text-lg font-bold text-purple-600">
            {formatCurrency(amount, currency)}
          </span>
        </div>
      </div>

      <div className="space-y-6">
        {/* Success Message */}
        {successMessage && (
          <div className="mb-4 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">{successMessage}</p>
              </div>
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    type="button"
                    onClick={() => setSuccessMessage(null)}
                    className="inline-flex bg-green-50 rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600"
                  >
                    <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {errorMessage && (
          <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800">{errorMessage}</p>
              </div>
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    type="button"
                    onClick={() => setErrorMessage(null)}
                    className="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600"
                  >
                    <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Stripe Card Element */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: '16px',
                  color: '#424770',
                  '::placeholder': {
                    color: '#aab7c4',
                  },
                },
                invalid: {
                  color: '#9e2146',
                },
              },
            }}
          />
        </div>



        {/* Submit Button */}
        <button
          type="button"
          onClick={handleSubmit}
          disabled={isLoading || !stripe || !elements}
          className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold py-3 px-6 rounded-lg hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Processing...</span>
            </>
          ) : (
            <>
              <CreditCardIcon className="h-4 w-4" />
              <span>Pay {formatCurrency(amount, currency)}</span>
            </>
          )}
        </button>
      </div>

      {/* Security Notice */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          Your payment information is encrypted and secure. We never store your card details.
        </p>
      </div>
    </div>
  )
}
