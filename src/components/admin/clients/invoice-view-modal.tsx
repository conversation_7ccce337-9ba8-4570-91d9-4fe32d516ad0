'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  XMarkIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  UserIcon,
  BuildingOfficeIcon,
  BanknotesIcon,
  ReceiptPercentIcon,
  CreditCardIcon,
  ArrowDownTrayIcon,
  PaperAirplaneIcon,
  PencilIcon
} from '@heroicons/react/24/outline'

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Project {
  id: string | number
  name: string
  description: string
  status?: string
}

interface Invoice {
  id: string | number
  clientId: string | number
  projectId?: string | number
  totalAmount: number
  subtotal?: number
  taxRate: number
  taxAmount: number
  status: string
  dueDate: string
  description?: string
  paidAt?: string
  createdAt: string
  updatedAt?: string
  _count?: {
    payments: number
  }
  contract?: {
    id: string | number
    contName: string
  }
  project?: {
    id: string | number
    name: string
  }
  order?: {
    id: string | number
    orderTitle: string
  }
}

interface InvoiceViewModalProps {
  isOpen: boolean
  onClose: () => void
  invoice: Invoice
  client: Client
  project?: Project
}

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'paid':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'sent':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'draft':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'overdue':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
  }
}

const getStatusIcon = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'paid':
      return <CheckCircleIcon className="h-4 w-4" />
    case 'sent':
      return <PaperAirplaneIcon className="h-4 w-4" />
    case 'draft':
      return <DocumentTextIcon className="h-4 w-4" />
    case 'overdue':
      return <ExclamationTriangleIcon className="h-4 w-4" />
    case 'cancelled':
      return <XMarkIcon className="h-4 w-4" />
    default:
      return <ClockIcon className="h-4 w-4" />
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const isOverdue = (dueDate: string, status: string) => {
  return status?.toLowerCase() !== 'paid' && new Date(dueDate) < new Date()
}

export default function InvoiceViewModal({
  isOpen,
  onClose,
  invoice,
  client,
  project
}: InvoiceViewModalProps) {
  if (!isOpen) return null

  const overdue = isOverdue(invoice.dueDate, invoice.status)

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          {/* Background overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
            onClick={onClose}
          />

          {/* Modal panel */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative inline-block align-bottom bg-white rounded-lg shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-green-600 to-green-700 px-6 py-4 rounded-t-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <BanknotesIcon className="h-6 w-6 text-white" />
                  <h2 className="text-xl font-bold text-white">Invoice Details</h2>
                </div>
                <button
                  onClick={onClose}
                  className="text-white hover:text-gray-200 transition-colors"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
              {/* Invoice Header */}
              <div className="mb-8">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      Invoice #{invoice.id}
                    </h3>
                    {invoice.description && (
                      <p className="text-gray-600 leading-relaxed">{invoice.description}</p>
                    )}
                  </div>
                  <div className="ml-6 flex-shrink-0 flex flex-col items-end space-y-2">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(invoice.status)}`}>
                      {getStatusIcon(invoice.status)}
                      <span className="ml-1 capitalize">{invoice.status}</span>
                    </span>
                    {overdue && (
                      <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
                        Overdue
                      </span>
                    )}
                  </div>
                </div>

                {/* Amount Summary */}
                <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border border-green-200">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">
                      {formatCurrency(invoice.totalAmount)}
                    </div>
                    <div className="text-sm text-gray-600">Total Amount</div>
                  </div>
                </div>
              </div>

              {/* Main Content Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Left Column - Invoice Details */}
                <div className="lg:col-span-2 space-y-6">
                  {/* Financial Breakdown */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <ReceiptPercentIcon className="h-5 w-5 mr-2 text-green-600" />
                      Financial Breakdown
                    </h4>
                    <div className="space-y-3">
                      {invoice.subtotal && (
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Subtotal</span>
                          <span className="font-semibold text-gray-900">{formatCurrency(invoice.subtotal)}</span>
                        </div>
                      )}
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Tax Rate</span>
                        <span className="font-semibold text-gray-900">{invoice.taxRate}%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Tax Amount</span>
                        <span className="font-semibold text-gray-900">{formatCurrency(invoice.taxAmount)}</span>
                      </div>
                      <div className="border-t border-gray-200 pt-3">
                        <div className="flex justify-between items-center">
                          <span className="text-lg font-semibold text-gray-900">Total Amount</span>
                          <span className="text-lg font-bold text-green-600">{formatCurrency(invoice.totalAmount)}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Timeline Section */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <CalendarIcon className="h-5 w-5 mr-2 text-blue-600" />
                      Invoice Timeline
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Created Date</label>
                        <div className="text-gray-900">{formatDate(invoice.createdAt)}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
                        <div className={`font-medium ${overdue ? 'text-red-600' : 'text-gray-900'}`}>
                          {formatDate(invoice.dueDate)}
                        </div>
                      </div>
                      {invoice.paidAt && (
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Paid Date</label>
                          <div className="text-green-600 font-medium">{formatDate(invoice.paidAt)}</div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Related Information */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Related Information</h4>
                    <div className="space-y-3">
                      {project && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Project</label>
                          <div className="text-gray-900">{project.name}</div>
                        </div>
                      )}
                      {invoice.contract && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Contract</label>
                          <div className="text-gray-900">{invoice.contract.contName}</div>
                        </div>
                      )}
                      {invoice.order && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Order</label>
                          <div className="text-gray-900">{invoice.order.orderTitle}</div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Right Column - Client & Meta Info */}
                <div className="space-y-6">
                  {/* Client Information */}
                  <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <BuildingOfficeIcon className="h-5 w-5 mr-2 text-blue-600" />
                      Client Information
                    </h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Company</label>
                        <div className="text-gray-900 font-medium">{client.companyName}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Contact Person</label>
                        <div className="text-gray-900 flex items-center">
                          <UserIcon className="h-4 w-4 mr-1 text-gray-500" />
                          {client.contactName}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <div className="text-gray-900">{client.contactEmail}</div>
                      </div>
                    </div>
                  </div>

                  {/* Payment Information */}
                  <div className="bg-green-50 rounded-lg p-6 border border-green-200">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <CreditCardIcon className="h-5 w-5 mr-2 text-green-600" />
                      Payment Information
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Payment Status</span>
                        <span className={`font-semibold ${invoice.status?.toLowerCase() === 'paid' ? 'text-green-600' : 'text-orange-600'}`}>
                          {invoice.status?.toLowerCase() === 'paid' ? 'Paid' : 'Pending'}
                        </span>
                      </div>
                      {invoice._count && (
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Payment Records</span>
                          <span className="font-semibold text-gray-900">{invoice._count.payments}</span>
                        </div>
                      )}
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Amount Due</span>
                        <span className={`font-bold ${invoice.status?.toLowerCase() === 'paid' ? 'text-green-600' : 'text-red-600'}`}>
                          {invoice.status?.toLowerCase() === 'paid' ? '$0.00' : formatCurrency(invoice.totalAmount)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Invoice Metadata */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Invoice Metadata</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Invoice ID</label>
                        <div className="text-gray-900 font-mono text-sm">{invoice.id}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Client ID</label>
                        <div className="text-gray-900 font-mono text-sm">{invoice.clientId}</div>
                      </div>
                      {invoice.projectId && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Project ID</label>
                          <div className="text-gray-900 font-mono text-sm">{invoice.projectId}</div>
                        </div>
                      )}
                      {invoice.updatedAt && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                          <div className="text-gray-900">{formatDate(invoice.updatedAt)}</div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                    <div className="space-y-3">
                      <button className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                        Download PDF
                      </button>
                      <button className="w-full flex items-center justify-center px-4 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors">
                        <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                        Send Invoice
                      </button>
                      <button className="w-full flex items-center justify-center px-4 py-2 border border-green-300 rounded-md text-sm font-medium text-green-700 bg-green-50 hover:bg-green-100 transition-colors">
                        <PencilIcon className="h-4 w-4 mr-2" />
                        Edit Invoice
                      </button>
                      {invoice.status?.toLowerCase() !== 'paid' && (
                        <button className="w-full flex items-center justify-center px-4 py-2 border border-green-300 rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 transition-colors">
                          <CheckCircleIcon className="h-4 w-4 mr-2" />
                          Mark as Paid
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-50 px-6 py-4 rounded-b-lg border-t border-gray-200">
              <div className="flex justify-end">
                <button
                  onClick={onClose}
                  className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
