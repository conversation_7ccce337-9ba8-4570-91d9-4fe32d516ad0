'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  XMarkIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  UserIcon,
  BuildingOfficeIcon,
  ChatBubbleLeftRightIcon,
  PhoneIcon,
  EnvelopeIcon,
  VideoCameraIcon,
  ExclamationCircleIcon,
  FireIcon,
  ArrowTrendingUpIcon,
  TagIcon,
  Cog6ToothIcon,
  SparklesIcon,
  BanknotesIcon
} from '@heroicons/react/24/outline'

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Quotation {
  id: string | number
  clientId: string | number
  serviceId: string
  serviceName: string
  selectedOptions: string[]
  selectedFeatures: string[]
  budget: number
  timeline: string
  description: string
  contactPreference: 'email' | 'phone' | 'meeting'
  urgency: 'low' | 'medium' | 'high'
  estimatedCost?: number
  serviceDetails?: any
  status: string
  requestDate: string
  responseDate?: string
  quotedAmount?: number
  notes?: string
  createdAt: string
  updatedAt?: string
}

interface QuotationViewModalProps {
  isOpen: boolean
  onClose: () => void
  quotation: Quotation
  client: Client
}

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'accepted':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'quoted':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'reviewed':
      return 'bg-purple-100 text-purple-800 border-purple-200'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'rejected':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getStatusIcon = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'accepted':
      return <CheckCircleIcon className="h-4 w-4" />
    case 'quoted':
      return <BanknotesIcon className="h-4 w-4" />
    case 'reviewed':
      return <DocumentTextIcon className="h-4 w-4" />
    case 'pending':
      return <ClockIcon className="h-4 w-4" />
    case 'rejected':
      return <ExclamationTriangleIcon className="h-4 w-4" />
    default:
      return <ExclamationCircleIcon className="h-4 w-4" />
  }
}

const getUrgencyColor = (urgency: string) => {
  switch (urgency?.toLowerCase()) {
    case 'high':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'medium':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'low':
      return 'bg-green-100 text-green-800 border-green-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getUrgencyIcon = (urgency: string) => {
  switch (urgency?.toLowerCase()) {
    case 'high':
      return <FireIcon className="h-4 w-4" />
    case 'medium':
      return <ArrowTrendingUpIcon className="h-4 w-4" />
    case 'low':
      return <ClockIcon className="h-4 w-4" />
    default:
      return <ExclamationCircleIcon className="h-4 w-4" />
  }
}

const getContactPreferenceIcon = (preference: string) => {
  switch (preference?.toLowerCase()) {
    case 'email':
      return <EnvelopeIcon className="h-4 w-4" />
    case 'phone':
      return <PhoneIcon className="h-4 w-4" />
    case 'meeting':
      return <VideoCameraIcon className="h-4 w-4" />
    default:
      return <ChatBubbleLeftRightIcon className="h-4 w-4" />
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export default function QuotationViewModal({
  isOpen,
  onClose,
  quotation,
  client
}: QuotationViewModalProps) {
  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          {/* Background overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
            onClick={onClose}
          />

          {/* Modal panel */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative inline-block align-bottom bg-white rounded-lg shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-indigo-600 to-indigo-700 px-6 py-4 rounded-t-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <DocumentTextIcon className="h-6 w-6 text-white" />
                  <h2 className="text-xl font-bold text-white">Quotation Request Details</h2>
                </div>
                <button
                  onClick={onClose}
                  className="text-white hover:text-gray-200 transition-colors"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
              {/* Quotation Header */}
              <div className="mb-8">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {quotation.serviceName}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">{quotation.description}</p>
                  </div>
                  <div className="ml-6 flex-shrink-0 flex flex-col items-end space-y-2">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(quotation.status)}`}>
                      {getStatusIcon(quotation.status)}
                      <span className="ml-1 capitalize">{quotation.status}</span>
                    </span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getUrgencyColor(quotation.urgency)}`}>
                      {getUrgencyIcon(quotation.urgency)}
                      <span className="ml-1 capitalize">{quotation.urgency} Priority</span>
                    </span>
                  </div>
                </div>

                {/* Budget & Quote Summary */}
                <div className="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-6 border border-indigo-200">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-indigo-600">
                        {formatCurrency(quotation.budget)}
                      </div>
                      <div className="text-sm text-gray-600">Client Budget</div>
                    </div>
                    {quotation.estimatedCost && (
                      <div>
                        <div className="text-2xl font-bold text-purple-600">
                          {formatCurrency(quotation.estimatedCost)}
                        </div>
                        <div className="text-sm text-gray-600">Estimated Cost</div>
                      </div>
                    )}
                    {quotation.quotedAmount && (
                      <div>
                        <div className="text-2xl font-bold text-green-600">
                          {formatCurrency(quotation.quotedAmount)}
                        </div>
                        <div className="text-sm text-gray-600">Quoted Amount</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Main Content Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Left Column - Service Details */}
                <div className="lg:col-span-2 space-y-6">
                  {/* Service Information */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <Cog6ToothIcon className="h-5 w-5 mr-2 text-indigo-600" />
                      Service Information
                    </h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Service ID</label>
                        <div className="text-gray-900 font-mono text-sm">{quotation.serviceId}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Service Name</label>
                        <div className="text-gray-900 font-medium">{quotation.serviceName}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Timeline</label>
                        <div className="text-gray-900">{quotation.timeline}</div>
                      </div>
                    </div>
                  </div>

                  {/* Selected Options */}
                  {quotation.selectedOptions && quotation.selectedOptions.length > 0 && (
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <TagIcon className="h-5 w-5 mr-2 text-blue-600" />
                        Selected Options
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {quotation.selectedOptions.map((option, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                          >
                            {option}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Selected Features */}
                  {quotation.selectedFeatures && quotation.selectedFeatures.length > 0 && (
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <SparklesIcon className="h-5 w-5 mr-2 text-purple-600" />
                        Selected Features
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {quotation.selectedFeatures.map((feature, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Timeline Section */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <CalendarIcon className="h-5 w-5 mr-2 text-green-600" />
                      Request Timeline
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Request Date</label>
                        <div className="text-gray-900">{formatDate(quotation.requestDate)}</div>
                      </div>
                      {quotation.responseDate && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Response Date</label>
                          <div className="text-gray-900">{formatDate(quotation.responseDate)}</div>
                        </div>
                      )}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Created</label>
                        <div className="text-gray-900">{formatDate(quotation.createdAt)}</div>
                      </div>
                      {quotation.updatedAt && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                          <div className="text-gray-900">{formatDate(quotation.updatedAt)}</div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Notes */}
                  {quotation.notes && (
                    <div className="bg-yellow-50 rounded-lg p-6 border border-yellow-200">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">Notes</h4>
                      <p className="text-gray-700 leading-relaxed">{quotation.notes}</p>
                    </div>
                  )}
                </div>

                {/* Right Column - Client & Meta Info */}
                <div className="space-y-6">
                  {/* Client Information */}
                  <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <BuildingOfficeIcon className="h-5 w-5 mr-2 text-blue-600" />
                      Client Information
                    </h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Company</label>
                        <div className="text-gray-900 font-medium">{client.companyName}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Contact Person</label>
                        <div className="text-gray-900 flex items-center">
                          <UserIcon className="h-4 w-4 mr-1 text-gray-500" />
                          {client.contactName}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <div className="text-gray-900">{client.contactEmail}</div>
                      </div>
                    </div>
                  </div>

                  {/* Contact Preferences */}
                  <div className="bg-green-50 rounded-lg p-6 border border-green-200">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <ChatBubbleLeftRightIcon className="h-5 w-5 mr-2 text-green-600" />
                      Contact Preferences
                    </h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Preferred Method</label>
                        <div className="flex items-center text-gray-900">
                          {getContactPreferenceIcon(quotation.contactPreference)}
                          <span className="ml-2 capitalize">{quotation.contactPreference}</span>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Urgency Level</label>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getUrgencyColor(quotation.urgency)}`}>
                          {getUrgencyIcon(quotation.urgency)}
                          <span className="ml-1 capitalize">{quotation.urgency}</span>
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Quotation Metadata */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Quotation Metadata</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Quotation ID</label>
                        <div className="text-gray-900 font-mono text-sm">{quotation.id}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Client ID</label>
                        <div className="text-gray-900 font-mono text-sm">{quotation.clientId}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Current Status</label>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(quotation.status)}`}>
                          {getStatusIcon(quotation.status)}
                          <span className="ml-1 capitalize">{quotation.status}</span>
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Service Details */}
                  {quotation.serviceDetails && (
                    <div className="bg-purple-50 rounded-lg p-6 border border-purple-200">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">Additional Service Details</h4>
                      <div className="text-sm text-gray-700">
                        <pre className="whitespace-pre-wrap font-mono text-xs bg-white p-3 rounded border">
                          {JSON.stringify(quotation.serviceDetails, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}

                  {/* Quick Actions */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                    <div className="space-y-2">
                      <button className="w-full px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-md transition-colors">
                        Create Quote
                      </button>
                      <button className="w-full px-4 py-2 text-sm font-medium text-indigo-600 bg-white border border-indigo-600 hover:bg-indigo-50 rounded-md transition-colors">
                        Convert to Project
                      </button>
                      <button className="w-full px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 hover:bg-gray-50 rounded-md transition-colors">
                        Send Message
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-50 px-6 py-4 rounded-b-lg border-t border-gray-200">
              <div className="flex justify-end">
                <button
                  onClick={onClose}
                  className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
