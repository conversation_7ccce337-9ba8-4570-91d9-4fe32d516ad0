'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  XMarkIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ClockIcon,
  GlobeAltIcon,
  CodeBracketIcon,
  TagIcon,
  UserIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  EyeIcon,
  LinkIcon
} from '@heroicons/react/24/outline'

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Project {
  id: string | number
  clientId: string | number
  name: string
  description: string
  status?: string
  projstartdate?: string
  projcompletiondate?: string
  estimatecost?: number
  estimatetime?: string
  imageurl?: string
  projecturl?: string
  githuburl?: string
  tags?: string
  createdat: string
  updatedat?: string
  _count?: {
    contracts: number
    invoices: number
    messages: number
  }
}

interface ProjectViewModalProps {
  isOpen: boolean
  onClose: () => void
  project: Project
  client: Client
}

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'in_progress':
    case 'active':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'planning':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'on_hold':
      return 'bg-orange-100 text-orange-800 border-orange-200'
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getStatusIcon = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
      return <CheckCircleIcon className="h-4 w-4" />
    case 'in_progress':
    case 'active':
      return <PlayIcon className="h-4 w-4" />
    case 'planning':
      return <DocumentTextIcon className="h-4 w-4" />
    case 'on_hold':
      return <PauseIcon className="h-4 w-4" />
    case 'cancelled':
      return <StopIcon className="h-4 w-4" />
    default:
      return <ExclamationTriangleIcon className="h-4 w-4" />
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export default function ProjectViewModal({
  isOpen,
  onClose,
  project,
  client
}: ProjectViewModalProps) {
  if (!isOpen) return null

  const tags = project.tags ? project.tags.split(',').map(tag => tag.trim()).filter(Boolean) : []

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          {/* Background overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
            onClick={onClose}
          />

          {/* Modal panel */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative inline-block align-bottom bg-white rounded-lg shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 rounded-t-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <DocumentTextIcon className="h-6 w-6 text-white" />
                  <h2 className="text-xl font-bold text-white">Project Details</h2>
                </div>
                <button
                  onClick={onClose}
                  className="text-white hover:text-gray-200 transition-colors"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
              {/* Project Header */}
              <div className="mb-8">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{project.name}</h3>
                    <p className="text-gray-600 leading-relaxed">{project.description}</p>
                  </div>
                  <div className="ml-6 flex-shrink-0">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(project.status || 'unknown')}`}>
                      {getStatusIcon(project.status || 'unknown')}
                      <span className="ml-1 capitalize">{project.status?.replace('_', ' ') || 'Unknown'}</span>
                    </span>
                  </div>
                </div>

                {/* Project Image */}
                {project.imageurl && (
                  <div className="mb-6">
                    <img
                      src={project.imageurl}
                      alt={project.name}
                      className="w-full h-64 object-cover rounded-lg border border-gray-200"
                    />
                  </div>
                )}
              </div>

              {/* Main Content Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Left Column - Project Details */}
                <div className="lg:col-span-2 space-y-6">
                  {/* Timeline Section */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <CalendarIcon className="h-5 w-5 mr-2 text-blue-600" />
                      Project Timeline
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <div className="text-gray-900">
                          {project.projstartdate ? formatDate(project.projstartdate) : 'Not set'}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Completion Date</label>
                        <div className="text-gray-900">
                          {project.projcompletiondate ? formatDate(project.projcompletiondate) : 'Not set'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Budget & Time Section */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <CurrencyDollarIcon className="h-5 w-5 mr-2 text-green-600" />
                      Budget & Timeline
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Estimated Cost</label>
                        <div className="text-lg font-semibold text-green-600">
                          {project.estimatecost ? formatCurrency(project.estimatecost) : 'Not specified'}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Estimated Time</label>
                        <div className="text-gray-900 flex items-center">
                          <ClockIcon className="h-4 w-4 mr-1 text-gray-500" />
                          {project.estimatetime || 'Not specified'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Links Section */}
                  {(project.projecturl || project.githuburl) && (
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <LinkIcon className="h-5 w-5 mr-2 text-purple-600" />
                        Project Links
                      </h4>
                      <div className="space-y-3">
                        {project.projecturl && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Live Project</label>
                            <a
                              href={project.projecturl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                            >
                              <GlobeAltIcon className="h-4 w-4 mr-1" />
                              View Live Site
                            </a>
                          </div>
                        )}
                        {project.githuburl && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Source Code</label>
                            <a
                              href={project.githuburl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center text-gray-700 hover:text-gray-900 transition-colors"
                            >
                              <CodeBracketIcon className="h-4 w-4 mr-1" />
                              View on GitHub
                            </a>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Tags Section */}
                  {tags.length > 0 && (
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <TagIcon className="h-5 w-5 mr-2 text-indigo-600" />
                        Project Tags
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {tags.map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Right Column - Client & Meta Info */}
                <div className="space-y-6">
                  {/* Client Information */}
                  <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <BuildingOfficeIcon className="h-5 w-5 mr-2 text-blue-600" />
                      Client Information
                    </h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Company</label>
                        <div className="text-gray-900 font-medium">{client.companyName}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Contact Person</label>
                        <div className="text-gray-900 flex items-center">
                          <UserIcon className="h-4 w-4 mr-1 text-gray-500" />
                          {client.contactName}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <div className="text-gray-900">{client.contactEmail}</div>
                      </div>
                    </div>
                  </div>

                  {/* Project Statistics */}
                  {project._count && (
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">Project Statistics</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Contracts</span>
                          <span className="font-semibold text-gray-900">{project._count.contracts}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Invoices</span>
                          <span className="font-semibold text-gray-900">{project._count.invoices}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Messages</span>
                          <span className="font-semibold text-gray-900">{project._count.messages}</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Project Metadata */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Project Metadata</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Project ID</label>
                        <div className="text-gray-900 font-mono text-sm">{project.id}</div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Created</label>
                        <div className="text-gray-900">{formatDate(project.createdat)}</div>
                      </div>
                      {project.updatedat && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                          <div className="text-gray-900">{formatDate(project.updatedat)}</div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-50 px-6 py-4 rounded-b-lg border-t border-gray-200">
              <div className="flex justify-end">
                <button
                  onClick={onClose}
                  className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
