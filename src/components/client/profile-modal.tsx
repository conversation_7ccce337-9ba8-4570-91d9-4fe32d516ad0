'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  XMarkIcon,
  UserIcon,
  BuildingOfficeIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  GlobeAltIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

const profileSchema = z.object({
  companyname: z.string().min(1, 'Company name is required'),
  contactname: z.string().min(1, 'Contact name is required'),
  contactemail: z.string().email('Please enter a valid email address'),
  contactphone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipcode: z.string().optional(),
  country: z.string().optional(),
  companywebsite: z.string().optional(),
  notes: z.string().optional()
})

type ProfileFormData = z.infer<typeof profileSchema>

interface ProfileModalProps {
  isOpen: boolean
  onClose: () => void
  client: any
  onSubmit: (data: ProfileFormData) => Promise<void>
}



export default function ProfileModal({ isOpen, onClose, client, onSubmit }: ProfileModalProps) {
  const [loading, setLoading] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [canClose, setCanClose] = useState(false)

  console.log('=== PROFILE MODAL RENDER ===')
  console.log('isOpen:', isOpen)
  console.log('client:', client)
  console.log('client exists:', !!client)

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isDirty }
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      companyname: '',
      contactname: '',
      contactemail: '',
      contactphone: '',
      address: '',
      city: '',
      state: '',
      zipcode: '',
      country: '',
      companywebsite: '',
      notes: ''
    }
  })

  // Reset form with client data when modal opens
  useEffect(() => {
    if (isOpen && client) {
      setSubmitError(null) // Clear any previous errors
      setCanClose(false) // Prevent immediate closing
      reset({
        companyname: client.companyname || '',
        contactname: client.contactname || '',
        contactemail: client.contactemail || '',
        contactphone: client.contactphone || '',
        address: client.address || '',
        city: client.city || '',
        state: client.state || '',
        zipcode: client.zipcode || '',
        country: client.country || '',
        companywebsite: client.companywebsite || client.website || '', // Handle both field names for compatibility
        notes: client.notes || ''
      })

      // Allow closing after a short delay to prevent immediate background clicks
      const timer = setTimeout(() => {
        setCanClose(true)
      }, 300)

      return () => clearTimeout(timer)
    }
  }, [isOpen, client, reset])

  const onSubmitForm = async (data: ProfileFormData) => {
    try {
      setLoading(true)
      setSubmitError(null) // Clear any previous errors
      await onSubmit(data)
      onClose()
    } catch (error) {
      console.error('Profile update error:', error)
      setSubmitError(error instanceof Error ? error.message : 'Failed to update profile. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) {
    console.log('ProfileModal: isOpen is false, returning null')
    return null
  }

  console.log('ProfileModal: isOpen is true, rendering modal')

  return (
    <AnimatePresence>
      <div
        className="fixed inset-0 z-50 overflow-y-auto"
        onClick={(e) => {
          // Only close if clicking the outer container, not its children
          if (e.target === e.currentTarget && canClose) {
            console.log('Outer container clicked, closing modal')
            onClose()
          }
        }}
      >
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            onClick={(e) => {
              // Prevent clicks inside the modal from bubbling up
              e.stopPropagation()
            }}
            className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full sm:p-6"
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <UserIcon className="h-6 w-6 text-blue-600 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">Edit Profile</h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Error Message */}
            {submitError && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <XMarkIcon className="h-5 w-5 text-red-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{submitError}</p>
                  </div>
                </div>
              </div>
            )}

            <form onSubmit={handleSubmit(onSubmitForm)} className="space-y-6">
              {/* Company Information */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-4 flex items-center">
                  <BuildingOfficeIcon className="h-4 w-4 mr-2" />
                  Company Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company Name *
                    </label>
                    <Controller
                      name="companyname"
                      control={control}
                      render={({ field }) => (
                        <input
                          {...field}
                          type="text"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Enter company name"
                        />
                      )}
                    />
                    {errors.companyname && (
                      <p className="mt-1 text-sm text-red-600">{errors.companyname.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company Website
                    </label>
                    <Controller
                      name="companywebsite"
                      control={control}
                      render={({ field }) => (
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <GlobeAltIcon className="h-4 w-4 text-gray-400" />
                          </div>
                          <input
                            {...field}
                            type="url"
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="https://example.com"
                          />
                        </div>
                      )}
                    />
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-4 flex items-center">
                  <UserIcon className="h-4 w-4 mr-2" />
                  Contact Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Contact Name *
                    </label>
                    <Controller
                      name="contactname"
                      control={control}
                      render={({ field }) => (
                        <input
                          {...field}
                          type="text"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Enter contact name"
                        />
                      )}
                    />
                    {errors.contactname && (
                      <p className="mt-1 text-sm text-red-600">{errors.contactname.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address *
                    </label>
                    <Controller
                      name="contactemail"
                      control={control}
                      render={({ field }) => (
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <EnvelopeIcon className="h-4 w-4 text-gray-400" />
                          </div>
                          <input
                            {...field}
                            type="email"
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter email address"
                          />
                        </div>
                      )}
                    />
                    {errors.contactemail && (
                      <p className="mt-1 text-sm text-red-600">{errors.contactemail.message}</p>
                    )}
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number
                    </label>
                    <Controller
                      name="contactphone"
                      control={control}
                      render={({ field }) => (
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <PhoneIcon className="h-4 w-4 text-gray-400" />
                          </div>
                          <input
                            {...field}
                            type="tel"
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter phone number"
                          />
                        </div>
                      )}
                    />
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-4 flex items-center">
                  <MapPinIcon className="h-4 w-4 mr-2" />
                  Address Information
                </h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Street Address
                    </label>
                    <Controller
                      name="address"
                      control={control}
                      render={({ field }) => (
                        <input
                          {...field}
                          type="text"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Enter street address"
                        />
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        City
                      </label>
                      <Controller
                        name="city"
                        control={control}
                        render={({ field }) => (
                          <input
                            {...field}
                            type="text"
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter city"
                          />
                        )}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        State/Province
                      </label>
                      <Controller
                        name="state"
                        control={control}
                        render={({ field }) => (
                          <input
                            {...field}
                            type="text"
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter state"
                          />
                        )}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        ZIP/Postal Code
                      </label>
                      <Controller
                        name="zipcode"
                        control={control}
                        render={({ field }) => (
                          <input
                            {...field}
                            type="text"
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter ZIP code"
                          />
                        )}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Country
                    </label>
                    <Controller
                      name="country"
                      control={control}
                      render={({ field }) => (
                        <input
                          {...field}
                          type="text"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Enter country"
                        />
                      )}
                    />
                  </div>
                </div>
              </div>

              {/* Additional Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Additional Notes
                </label>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <textarea
                      {...field}
                      rows={3}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Any additional information..."
                    />
                  )}
                />
              </div>

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading || !isDirty}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
