'use client'

import React, { useState, useEffect } from 'react'

import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { CardElement, useStripe, useElements } from '@stripe/react-stripe-js'
import {
  XMarkIcon,
  CreditCardIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  BanknotesIcon,
  BuildingLibraryIcon,
  DevicePhoneMobileIcon,
  GlobeAltIcon,
  ChevronDownIcon,
  ShieldCheckIcon,
  LockClosedIcon,
  EnvelopeIcon,
  TagIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'

const basePaymentSchema = z.object({
  invoiceId: z.string().min(1, 'Please select an invoice'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  paymentMethod: z.string().min(1, 'Please select a payment method'),
  currency: z.string().default('USD'),
  description: z.string().optional(),
  promoCode: z.string().optional(),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms'),
  emailReceipt: z.boolean().default(true),
  // PayPal fields
  paypalEmail: z.string().optional(),
  // Bank transfer fields
  bankAccountName: z.string().optional(),
  bankAccountNumber: z.string().optional(),
  bankRoutingNumber: z.string().optional(),
  bankName: z.string().optional(),
  bankSwiftCode: z.string().optional()
})

const paymentSchema = basePaymentSchema.superRefine((data, ctx) => {
  // Validate PayPal fields
  if (data.paymentMethod === 'paypal') {
    if (!data.paypalEmail) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'PayPal email is required',
        path: ['paypalEmail']
      })
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.paypalEmail)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Please enter a valid email address',
        path: ['paypalEmail']
      })
    }
  }

  // Validate Bank Transfer fields
  if (data.paymentMethod === 'bank_transfer') {
    if (!data.bankAccountName) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Account holder name is required',
        path: ['bankAccountName']
      })
    }
    if (!data.bankAccountNumber) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Account number is required',
        path: ['bankAccountNumber']
      })
    }
    if (!data.bankRoutingNumber) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Routing number is required',
        path: ['bankRoutingNumber']
      })
    }
    if (!data.bankName) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Bank name is required',
        path: ['bankName']
      })
    }
  }
})

type PaymentFormData = z.infer<typeof paymentSchema>

interface PaymentModalProps {
  isOpen: boolean
  onClose: () => void
  invoices: any[]
  onSubmit: (data: any) => Promise<void>
}

const paymentMethods = [
  {
    id: 'stripe_card',
    name: 'Credit/Debit Card',
    description: 'Visa, Mastercard, American Express',
    icon: CreditCardIcon,
    processingFee: 2.9,
    requiresStripe: true
  },
  {
    id: 'paypal',
    name: 'PayPal',
    description: 'Pay with your PayPal account',
    icon: GlobeAltIcon,
    processingFee: 3.5,
    requiresStripe: false
  },
  {
    id: 'bank_transfer',
    name: 'Bank Transfer',
    description: 'Direct bank transfer',
    icon: BuildingLibraryIcon,
    processingFee: 0,
    requiresStripe: false
  }
]

// Stripe Card Component
function StripeCardForm({ clientSecret }: { clientSecret: string | null }) {
  const stripe = useStripe()
  const elements = useElements()

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
        fontFamily: 'Inter, system-ui, sans-serif',
        padding: '12px',
      },
      invalid: {
        color: '#9e2146',
      },
    },
    hidePostalCode: false,
  }

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Card Information
        </label>
        <div className="border border-gray-300 rounded-md p-3 bg-white">
          <CardElement options={cardElementOptions} />
        </div>
        <p className="mt-1 text-xs text-gray-500">
          Your payment information is secure and encrypted
        </p>
      </div>

      <div className="flex items-center space-x-2 text-xs text-gray-500">
        <ShieldCheckIcon className="h-4 w-4 text-green-500" />
        <span>Secured by Stripe</span>
        <LockClosedIcon className="h-4 w-4 text-gray-400" />
        <span>256-bit SSL encryption</span>
      </div>

      {clientSecret && (
        <div className="bg-green-50 border border-green-200 rounded-md p-3">
          <div className="flex">
            <CheckCircleIcon className="h-5 w-5 text-green-400 mr-2 mt-0.5" />
            <div className="text-sm text-green-700">
              <p className="font-medium">Payment Ready</p>
              <p className="mt-1">Stripe payment intent created successfully.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default function PaymentModal({ isOpen, onClose, invoices, onSubmit }: PaymentModalProps) {
  const [loading, setLoading] = useState(false)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(paymentMethods[0])
  const [clientSecret, setClientSecret] = useState<string | null>(null)
  const [appliedPromo, setAppliedPromo] = useState<any>(null)

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      invoiceId: '',
      amount: 0,
      currency: 'USD',
      promoCode: '',
      emailReceipt: true,
      acceptTerms: true,
      paymentMethod: '',
      paypalEmail: '',
      bankAccountName: '',
      bankAccountNumber: '',
      bankRoutingNumber: '',
      bankName: '',
      bankSwiftCode: ''
    }
  })

  const watchedInvoiceId = watch('invoiceId')
  const watchedAmount = watch('amount')
  const watchedPromoCode = watch('promoCode')

  const selectedInvoice = invoices.find(inv => inv.id.toString() === watchedInvoiceId)

  // Set initial payment method when modal opens
  useEffect(() => {
    if (isOpen && paymentMethods.length > 0) {
      setSelectedPaymentMethod(paymentMethods[0])
      setValue('paymentMethod', paymentMethods[0].id)
    }
  }, [isOpen, setValue])

  useEffect(() => {
    if (selectedInvoice) {
      setValue('amount', selectedInvoice.totalamount || 0)
    }
  }, [selectedInvoice, setValue])

  const calculateProcessingFee = (amount: number) => {
    return (amount * selectedPaymentMethod.processingFee) / 100
  }

  const calculateTotal = () => {
    const baseAmount = watchedAmount || 0
    const processingFee = calculateProcessingFee(baseAmount)
    const promoDiscount = appliedPromo ? (baseAmount * appliedPromo.discount) / 100 : 0
    return baseAmount + processingFee - promoDiscount
  }

  const handlePaymentMethodSelect = (method: typeof paymentMethods[0]) => {
    setSelectedPaymentMethod(method)
    setValue('paymentMethod', method.id)
    if (method.requiresStripe && watchedAmount > 0) {
      // Create Stripe payment intent
      createPaymentIntent()
    }
  }

  const createPaymentIntent = async () => {
    try {
      const selectedInvoice = invoices.find(inv => inv.id === watchedInvoiceId)
      const response = await fetch('/api/stripe/create-payment-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: calculateTotal(), // Send amount in dollars, API will convert to cents
          currency: 'usd',
          metadata: {
            invoiceId: watchedInvoiceId,
            projectId: selectedInvoice?.projectid || '',
            clientId: selectedInvoice?.clientid || '',
            paymentMethod: selectedPaymentMethod.id
          }
        })
      })
      
      if (response.ok) {
        const result = await response.json()
        const { clientSecret, mock } = result

        if (mock) {
          console.log('Mock payment mode - Stripe not configured')
          alert('Payment system is in development mode. Configure Stripe keys for real payments.')
        }

        setClientSecret(clientSecret)
      } else {
        const errorData = await response.json()
        console.error('Payment intent creation failed:', errorData)
        alert(`Payment setup failed: ${errorData.error}`)
      }
    } catch (error) {
      console.error('Error creating payment intent:', error)
      alert('Failed to initialize payment. Please try again.')
    }
  }

  const applyPromoCode = async () => {
    if (!watchedPromoCode) return

    try {
      const response = await fetch('/api/promo-codes/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code: watchedPromoCode })
      })

      if (response.ok) {
        const promo = await response.json()
        setAppliedPromo(promo)
      } else {
        setAppliedPromo(null)
        alert('Invalid promo code')
      }
    } catch (error) {
      console.error('Error validating promo code:', error)
      setAppliedPromo(null)
    }
  }

  const onSubmitForm = async (data: PaymentFormData) => {
    console.log('Form submission started with data:', data)
    console.log('Selected payment method:', selectedPaymentMethod)
    console.log('Form errors:', errors)

    try {
      setLoading(true)

      // Handle Stripe payment processing
      if (selectedPaymentMethod.id === 'stripe_card') {
        if (!clientSecret) {
          throw new Error('Payment not properly initialized. Please try again.')
        }

        // This will be handled by the Stripe CardElement
        // The actual payment confirmation will happen in the backend
        const paymentData = {
          ...data,
          paymentMethodId: selectedPaymentMethod.id,
          processingFee: calculateProcessingFee(data.amount),
          totalAmount: calculateTotal(),
          appliedPromo,
          clientSecret,
          paymentType: 'stripe_card'
        }

        await onSubmit(paymentData)
      } else {
        // Handle other payment methods (PayPal, Bank Transfer)
        const paymentData = {
          ...data,
          paymentMethodId: selectedPaymentMethod.id,
          processingFee: calculateProcessingFee(data.amount),
          totalAmount: calculateTotal(),
          appliedPromo,
          paymentType: selectedPaymentMethod.id
        }

        await onSubmit(paymentData)
      }

      reset()
      onClose()
    } catch (error) {
      console.error('Payment submission error:', error)
      alert(`Failed to process payment: ${error instanceof Error ? error.message : 'Please try again.'}`)
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Background overlay - no click handler */}
      <div className="fixed inset-0 bg-gray-500 bg-opacity-75" />

      {/* Modal container */}
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20">
        <div
          className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full mx-auto p-6"
          onClick={(e) => e.stopPropagation()}
        >
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <CreditCardIcon className="h-6 w-6 text-blue-600 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">Make Payment</h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={(e) => {
              console.log('Form submit event triggered')
              handleSubmit(onSubmitForm)(e)
            }} className="space-y-6">
              {/* Invoice Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Invoice
                </label>
                <Controller
                  name="invoiceId"
                  control={control}
                  render={({ field }) => (
                    <select
                      {...field}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Select an invoice</option>
                      {invoices.map((invoice) => (
                        <option key={invoice.id} value={invoice.id}>
                          {invoice.description} - {new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(invoice.totalamount || 0)}
                        </option>
                      ))}
                    </select>
                  )}
                />
                {errors.invoiceId && (
                  <p className="mt-1 text-sm text-red-600">{errors.invoiceId.message}</p>
                )}
              </div>

              {/* Amount */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Payment Amount
                </label>
                <Controller
                  name="amount"
                  control={control}
                  render={({ field }) => (
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <CurrencyDollarIcon className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        {...field}
                        type="number"
                        step="0.01"
                        min="0"
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="0.00"
                      />
                    </div>
                  )}
                />
                {errors.amount && (
                  <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
                )}
              </div>

              {/* Payment Method Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Payment Method
                </label>
                <div className="space-y-2">
                  {paymentMethods.map((method) => {
                    const Icon = method.icon
                    return (
                      <div
                        key={method.id}
                        onClick={() => handlePaymentMethodSelect(method)}
                        className={`relative rounded-lg border p-4 cursor-pointer hover:bg-gray-50 ${
                          selectedPaymentMethod.id === method.id
                            ? 'border-blue-500 ring-2 ring-blue-500'
                            : 'border-gray-300'
                        }`}
                      >
                        <div className="flex items-center">
                          <Icon className="h-6 w-6 text-gray-400 mr-3" />
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-gray-900">{method.name}</p>
                              <p className="text-xs text-gray-500">
                                {method.processingFee > 0 ? `${method.processingFee}% fee` : 'No fee'}
                              </p>
                            </div>
                            <p className="text-xs text-gray-500">{method.description}</p>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              {/* Payment Method Specific Fields */}
              {selectedPaymentMethod.id === 'stripe_card' && (
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Card Details</h4>
                  <StripeCardForm clientSecret={clientSecret} />
                </div>
              )}

              {selectedPaymentMethod.id === 'paypal' && (
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">PayPal Information</h4>
                  <div className="space-y-4">
                    <Controller
                      name="paypalEmail"
                      control={control}
                      render={({ field }) => (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            PayPal Email Address
                          </label>
                          <div className="relative">
                            <input
                              {...field}
                              type="email"
                              placeholder="<EMAIL>"
                              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            />
                            <EnvelopeIcon className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
                          </div>
                          {errors.paypalEmail && (
                            <p className="mt-1 text-sm text-red-600">{errors.paypalEmail.message}</p>
                          )}
                        </div>
                      )}
                    />

                    <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                      <div className="flex">
                        <InformationCircleIcon className="h-5 w-5 text-blue-400 mr-2 mt-0.5" />
                        <div className="text-sm text-blue-700">
                          <p className="font-medium">PayPal Payment Process:</p>
                          <p className="mt-1">You'll be redirected to PayPal to complete your payment securely.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {selectedPaymentMethod.id === 'bank_transfer' && (
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Bank Transfer Details</h4>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Controller
                        name="bankAccountName"
                        control={control}
                        render={({ field }) => (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Account Holder Name
                            </label>
                            <input
                              {...field}
                              type="text"
                              placeholder="John Doe"
                              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            />
                            {errors.bankAccountName && (
                              <p className="mt-1 text-sm text-red-600">{errors.bankAccountName.message}</p>
                            )}
                          </div>
                        )}
                      />

                      <Controller
                        name="bankName"
                        control={control}
                        render={({ field }) => (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Bank Name
                            </label>
                            <input
                              {...field}
                              type="text"
                              placeholder="Chase Bank"
                              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            />
                            {errors.bankName && (
                              <p className="mt-1 text-sm text-red-600">{errors.bankName.message}</p>
                            )}
                          </div>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Controller
                        name="bankAccountNumber"
                        control={control}
                        render={({ field }) => (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Account Number
                            </label>
                            <input
                              {...field}
                              type="text"
                              placeholder="**********"
                              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            />
                            {errors.bankAccountNumber && (
                              <p className="mt-1 text-sm text-red-600">{errors.bankAccountNumber.message}</p>
                            )}
                          </div>
                        )}
                      />

                      <Controller
                        name="bankRoutingNumber"
                        control={control}
                        render={({ field }) => (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Routing Number
                            </label>
                            <input
                              {...field}
                              type="text"
                              placeholder="*********"
                              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            />
                            {errors.bankRoutingNumber && (
                              <p className="mt-1 text-sm text-red-600">{errors.bankRoutingNumber.message}</p>
                            )}
                          </div>
                        )}
                      />
                    </div>

                    <Controller
                      name="bankSwiftCode"
                      control={control}
                      render={({ field }) => (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            SWIFT/BIC Code (Optional)
                          </label>
                          <input
                            {...field}
                            type="text"
                            placeholder="CHASUS33"
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          />
                          <p className="mt-1 text-xs text-gray-500">
                            Required for international transfers
                          </p>
                          {errors.bankSwiftCode && (
                            <p className="mt-1 text-sm text-red-600">{errors.bankSwiftCode.message}</p>
                          )}
                        </div>
                      )}
                    />

                    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                      <div className="flex">
                        <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mr-2 mt-0.5" />
                        <div className="text-sm text-yellow-700">
                          <p className="font-medium">Bank Transfer Notice:</p>
                          <p className="mt-1">Bank transfers may take 1-3 business days to process. You'll receive payment instructions via email.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Payment Summary */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Payment Summary</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Subtotal:</span>
                    <span className="text-gray-900">${(watchedAmount || 0).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Processing Fee:</span>
                    <span className="text-gray-900">${calculateProcessingFee(watchedAmount || 0).toFixed(2)}</span>
                  </div>
                  {appliedPromo && (
                    <div className="flex justify-between text-green-600">
                      <span>Promo Discount:</span>
                      <span>-${((watchedAmount || 0) * appliedPromo.discount / 100).toFixed(2)}</span>
                    </div>
                  )}
                  <div className="border-t border-gray-200 pt-2">
                    <div className="flex justify-between font-medium">
                      <span className="text-gray-900">Total:</span>
                      <span className="text-gray-900">${calculateTotal().toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Terms and Submit */}
              <div className="space-y-4">
                <Controller
                  name="acceptTerms"
                  control={control}
                  render={({ field }) => (
                    <div className="flex items-start">
                      <input
                        {...field}
                        type="checkbox"
                        checked={field.value}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                      />
                      <label className="ml-2 text-sm text-gray-600">
                        I agree to the <a href="#" className="text-blue-600 hover:text-blue-500">Terms of Service</a> and <a href="#" className="text-blue-600 hover:text-blue-500">Privacy Policy</a>
                      </label>
                    </div>
                  )}
                />
                {errors.acceptTerms && (
                  <p className="text-sm text-red-600">{errors.acceptTerms.message}</p>
                )}

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                  >
                    {loading ? 'Processing...' : `Pay $${calculateTotal().toFixed(2)}`}
                  </button>
                </div>
              </div>
            </form>
        </div>
      </div>
    </div>
  )
}
